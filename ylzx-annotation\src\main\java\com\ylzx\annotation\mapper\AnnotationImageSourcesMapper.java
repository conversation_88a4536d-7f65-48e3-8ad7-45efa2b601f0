package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationImageSources;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 标注图片来源Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationImageSourcesMapper extends BaseMapper<AnnotationImageSources>
{
    /**
     * 查询标注图片来源列表
     *
     * @param annotationImageSources 标注图片来源
     * @return 标注图片来源集合
     */
    List<AnnotationImageSources> selectAnnotationImageSourcesList(AnnotationImageSources annotationImageSources);

    /**
     * 检查内容哈希是否存在
     *
     * @param contentHash 内容哈希值
     * @return 存在的记录数量，0表示不存在，大于0表示存在
     */
    int countByContentHash(@Param("contentHash") String contentHash);




    /**
     * 新增标注图片来源
     *
     * @param annotationImageSources 标注图片来源
     * @return 结果
     */
    public int insertAnnotationImageSources(AnnotationImageSources annotationImageSources);

    /**
     * 修改标注图片来源
     *
     * @param annotationImageSources 标注图片来源
     * @return 结果
     */
    public int updateAnnotationImageSources(AnnotationImageSources annotationImageSources);

}

package com.ylzx.annotation.controller;

import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.service.AnnotationImagesService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@RestController
@RequestMapping("/preview")
public class DirectStreamController {

    @Resource
    private AnnotationImagesService AnnotationImagesService;

    private final String IMAGE_ROOT = System.getProperty("user.dir") ;
    @GetMapping("/**")
    public void previewImage(HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {

        String requestUri = request.getRequestURI();
        String decodedUri = java.net.URLDecoder.decode(requestUri, "UTF-8");
        String relativePath = decodedUri.substring(decodedUri.indexOf("/preview/") + 9);

        InputStream in = null;
        OutputStream out = null;
        try {
            // 1. 构建并校验文件路径
            Path imagePath = Paths.get(IMAGE_ROOT, relativePath).normalize();
            // 安全校验：防止访问根目录外的文件（如通过../跳转）
            if (!imagePath.startsWith(Paths.get(IMAGE_ROOT))) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "禁止访问");
                return;
            }
            File imageFile = imagePath.toFile();
            if (!imageFile.exists() || !imageFile.isFile()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
                return;
            }

            // 2. 设置响应头（关键：控制浏览器预览）
            // 2.1 自动获取图片MIME类型（如image/png、image/jpeg）
            String contentType = Files.probeContentType(imagePath);
            if (contentType == null || !contentType.startsWith("image/")) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "不是图片文件");
                return;
            }
            response.setContentType(contentType); // 告诉浏览器：这是图片

            // 2.2 控制浏览器直接显示（inline），而非下载（attachment）
            response.setHeader("Content-Disposition",
                    "inline; filename=\"" + imageFile.getName() + "\"");

            // 3. 读取文件流并写入response输出流
            in = new FileInputStream(imageFile);
            out = response.getOutputStream();
            byte[] buffer = new byte[1024 * 4]; // 4KB缓冲
            int len;
            // 循环读写：将文件流写入response流
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush(); // 刷新缓冲区，确保数据全部发送

        } catch (Exception e) {
            e.printStackTrace();
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器错误");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        } finally {
            // 4. 关闭流（防止资源泄漏）
            if (in != null) {
                try { in.close(); } catch (IOException e) { e.printStackTrace(); }
            }
            if (out != null) {
                try { out.close(); } catch (IOException e) { e.printStackTrace(); }
            }
        }
    }


    @GetMapping("/{imageId}")
    public void previewImageByImageId(@PathVariable("imageId") Long imageId, HttpServletResponse response) throws IOException {



        AnnotationImages images = AnnotationImagesService.selectAnnotationImagesByImageId(imageId);
        if (images == null){
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "文件不存在");
        }
        String relativePath = images.getStoragePath();
        InputStream in = null;
        OutputStream out = null;
        try {
            // 1. 构建并校验文件路径
            Path imagePath = Paths.get(IMAGE_ROOT, relativePath).normalize();
            // 安全校验：防止访问根目录外的文件（如通过../跳转）
            if (!imagePath.startsWith(Paths.get(IMAGE_ROOT))) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "禁止访问");
                return;
            }
            File imageFile = imagePath.toFile();
            if (!imageFile.exists() || !imageFile.isFile()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
                return;
            }

            // 2. 设置响应头（关键：控制浏览器预览）
            // 2.1 自动获取图片MIME类型（如image/png、image/jpeg）
            String contentType = Files.probeContentType(imagePath);
            if (contentType == null || !contentType.startsWith("image/")) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "不是图片文件");
                return;
            }
            response.setContentType(contentType); // 告诉浏览器：这是图片

            // 2.2 控制浏览器直接显示（inline），而非下载（attachment）
            response.setHeader("Content-Disposition",
                    "inline; filename=\"" + imageFile.getName() + "\"");

            // 3. 读取文件流并写入response输出流
            in = new FileInputStream(imageFile);
            out = response.getOutputStream();
            byte[] buffer = new byte[1024 * 4]; // 4KB缓冲
            int len;
            // 循环读写：将文件流写入response流
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush(); // 刷新缓冲区，确保数据全部发送

        } catch (Exception e) {
            e.printStackTrace();
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务器错误");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        } finally {
            // 4. 关闭流（防止资源泄漏）
            if (in != null) {
                try { in.close(); } catch (IOException e) { e.printStackTrace(); }
            }
            if (out != null) {
                try { out.close(); } catch (IOException e) { e.printStackTrace(); }
            }
        }
    }
}

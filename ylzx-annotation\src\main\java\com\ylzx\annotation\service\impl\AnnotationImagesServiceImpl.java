package com.ylzx.annotation.service.impl;

import java.util.List;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.annotation.domain.*;
import com.ylzx.annotation.mapper.AnnotationImageSourcesMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationImagesMapper;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.mapper.AnnotationCategoriesImagesMapper;
import com.ylzx.annotation.mapper.AnnotationCategoriesMapper;
import com.ylzx.annotation.config.AnnotationPathProperties;

/**
 * 标注图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationImagesServiceImpl extends ServiceImpl<AnnotationImagesMapper,AnnotationImages> implements AnnotationImagesService
{
    private static final Logger log = LoggerFactory.getLogger(AnnotationImagesServiceImpl.class);

    @Autowired
    private AnnotationImagesMapper annotationImagesMapper;

    @Autowired
    private AnnotationCategoriesImagesMapper categoriesImagesMapper;
    
    @Autowired
    private AnnotationCategoriesMapper categoriesMapper;

    @Autowired
    private ExecutorService virtualThreadExecutor;

    @Autowired
    private AnnotationPathProperties pathProperties;

    @Autowired
    private AnnotationImageSourcesMapper annotationImageSourcesMapper;


    @Autowired
    private AnnotationCategoriesMapper annotationCategoriesMapper;

    private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList(
            ".png", ".jpg", ".jpeg", ".gif", ".webp", ".heic", ".heif"
    );

    /**
     * 查询标注图片
     * 
     * @param imageId 标注图片主键
     * @return 标注图片
     */
    @Override
    public AnnotationImages selectAnnotationImagesByImageId(Long imageId)
    {
        return annotationImagesMapper.selectAnnotationImagesByImageId(imageId);
    }

    /**
     * 查询标注图片列表
     * 
     * @param annotationImages 标注图片
     * @return 标注图片集合
     */
    @Override
    public List<AnnotationImages> selectAnnotationImagesList(AnnotationImages annotationImages)
    {
        List<AnnotationImages> annotationImagesList = annotationImagesMapper.selectAnnotationImagesList(annotationImages);


        List<AnnotationCategories> annotationCategoriesList = annotationCategoriesMapper.selectAnnotationCategoriesList(new AnnotationCategories());
        List<AnnotationImageSources> annotationImageSourcesList = annotationImageSourcesMapper.selectAnnotationImageSourcesList(AnnotationImageSources.builder().build());
        for(AnnotationImages images : annotationImagesList){

            // 设置分类名称
            for(AnnotationCategories category : annotationCategoriesList){
                if(images.getCategoryId().equals(category.getCategoryId())){
                    images.setCategoryName(category.getName());
                    break;
                }
            }
            // 设置数据源名称
            for(AnnotationImageSources sources : annotationImageSourcesList){
                if(images.getSourceId().equals(sources.getSourceId())){
                    images.setSourceName(sources.getSourceName());
                    break;
                }
            }
        }

        return annotationImagesList;
    }

    /**
     * 新增标注图片
     * 
     * @param annotationImages 标注图片
     * @return 结果
     */
    @Override
    public int insertAnnotationImages(AnnotationImages annotationImages)
    {
        return annotationImagesMapper.insertAnnotationImages(annotationImages);
    }

    /**
     * 修改标注图片
     * 
     * @param annotationImages 标注图片
     * @return 结果
     */
    @Override
    public int updateAnnotationImages(AnnotationImages annotationImages)
    {
        return annotationImagesMapper.updateAnnotationImages(annotationImages);
    }

    /**
     * 批量删除标注图片
     * 
     * @param imageIds 需要删除的标注图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImagesByImageIds(Long[] imageIds)
    {
        return annotationImagesMapper.deleteAnnotationImagesByImageIds(imageIds);
    }

    /**
     * 删除标注图片信息
     * 
     * @param imageId 标注图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImagesByImageId(Long imageId)
    {
        return annotationImagesMapper.deleteAnnotationImagesByImageId(imageId);
    }

    @Override
    public void processImagesFromSourceId(Long sourceId, Long categoryId) {

//        AnnotationImageSources source = annotationImageSourcesMapper.selectById(sourceId);
        List<AnnotationImageSources> annotationImageSources = annotationImageSourcesMapper.selectAnnotationImageSourcesList(AnnotationImageSources.builder().sourceId(sourceId).build());

        AnnotationImageSources source = annotationImageSources.get(0);
        if (source == null || source.getPath() == null || source.getPath().isEmpty()) {
            log.error("数据源未找到或其路径为空，sourceId: {}", sourceId);
            throw new IllegalArgumentException("数据源未找到或其路径为空，sourceId: " + sourceId);
        }
        // source.getPath() 现在是已经解压好的目录路径
        processImagesInDirectory(Paths.get(source.getPath()), sourceId, categoryId);
    }

    private void processImagesInDirectory(Path directoryPath, Long sourceId, Long categoryId) {
        // 解析路径：如果是相对路径，则基于 annotation_data 根目录进行解析
        Path resolvedPath = resolveDirectoryPath(directoryPath);

        try {
            log.info("开始处理目录 {} 中的图片", resolvedPath);
            List<AnnotationImages> imagesToStore;
            try (Stream<Path> pathStream = Files.walk(resolvedPath)) {
                List<CompletableFuture<AnnotationImages>> futures = pathStream
                        .filter(Files::isRegularFile)
                        .filter(this::isSupportedImage)
                        .map(imagePath -> CompletableFuture.supplyAsync(() -> {
                            try {
                                AnnotationImages image = new AnnotationImages();
                                image.setSourceId(sourceId);
                                image.setCategoryId(categoryId);
                                image.setStoragePath(imagePath.toString());
                                image.setOriginalFilename(imagePath.getFileName().toString());
                                image.setFileSizeBytes(Files.size(imagePath));

                                // Read image dimensions
                                try (InputStream is = Files.newInputStream(imagePath)) {
                                    BufferedImage bimg = ImageIO.read(is);
                                    if (bimg != null) {
                                        image.setWidth((long) bimg.getWidth());
                                        image.setHeight((long) bimg.getHeight());
                                    } else {
                                        log.warn("无法读取图片尺寸: {}", imagePath);
                                    }
                                } catch (IOException e) {
                                    log.error("读取图片元数据时出错: {}", imagePath, e);
                                    return null;
                                }

                                // Calculate MD5 hash
                                try (InputStream is = Files.newInputStream(imagePath)) {
                                    MessageDigest md = MessageDigest.getInstance("MD5");
                                    byte[] buffer = new byte[8192];
                                    int bytesRead;
                                    while ((bytesRead = is.read(buffer)) != -1) {
                                        md.update(buffer, 0, bytesRead);
                                    }
                                    byte[] digest = md.digest();
                                    StringBuilder sb = new StringBuilder();
                                    for (byte b : digest) {
                                        sb.append(String.format("%02x", b));
                                    }
                                    String md5Hash = sb.toString();
                                    image.setMd5Hash(md5Hash);

                                    // 检查MD5是否重复
                                    int count = annotationImagesMapper.countByMd5Hash(md5Hash);
                                    if (count > 0) {
                                        log.debug("跳过重复图片（MD5已存在）: {} - MD5: {}", imagePath, md5Hash);
                                        return null;
                                    }
                                } catch (NoSuchAlgorithmException | IOException e) {
                                    log.error("计算MD5时出错: {}", imagePath, e);
                                    return null;
                                }

                                return image;
                            } catch (IOException e) {
                                log.error("处理文件时出错: {}", imagePath, e);
                                return null;
                            }
                        }, virtualThreadExecutor))
                        .toList();

                imagesToStore = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }

            // 分批保存图片信息，每200条保存一次
            if (!imagesToStore.isEmpty()) {
                final int batchSize = 200;
                log.info("正在向数据库分批保存 {} 张图片，每批 {} 条。", imagesToStore.size(), batchSize);

                // 分批保存图片信息
                for (int i = 0; i < imagesToStore.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, imagesToStore.size());
                    List<AnnotationImages> batch = imagesToStore.subList(i, endIndex);
                    log.info("正在保存第 {} 批图片，数量: {} (总进度: {}/{})",
                            (i / batchSize + 1), batch.size(), endIndex, imagesToStore.size());
                    annotationImagesMapper.insertBatch(batch);
                    System.out.println();
                }

                // 再次验证分类是否存在，防止处理过程中分类被删除
                AnnotationCategories category = categoriesMapper.selectAnnotationCategoriesByCategoryId(categoryId);
                if (category == null) {
                    log.warn("跳过关联关系保存，分类不存在，categoryId: {}", categoryId);
                } else {

                    List<AnnotationImages> annotationImages = annotationImagesMapper.selectAnnotationImagesList(AnnotationImages.builder().build());
                    imagesToStore.stream().forEach(image -> {
                        AnnotationImages annotationImages1 = annotationImages.stream().filter(img -> img.getMd5Hash().equals(image.getMd5Hash())).collect(Collectors.toList()).get(0);
                        if (annotationImages1 != null){
                            image.setImageId(annotationImages1.getImageId());
                        }
                    });



                    // 保存图片与分类的关联关系
                    List<AnnotationCategoriesImages> categoriesToStore = imagesToStore.stream()
                            .map(image -> {
                                AnnotationCategoriesImages categoryImage = new AnnotationCategoriesImages();
                                categoryImage.setCategoryId(categoryId);
                                categoryImage.setImageId(image.getImageId());
                                return categoryImage;
                            })
                            .collect(Collectors.toList());

                    log.info("正在分批保存 {} 条分类-图片关联记录，每批 {} 条。", categoriesToStore.size(), batchSize);
                    // 分批保存关联关系
                    for (int i = 0; i < categoriesToStore.size(); i += batchSize) {
                        int endIndex = Math.min(i + batchSize, categoriesToStore.size());
                        List<AnnotationCategoriesImages> batch = categoriesToStore.subList(i, endIndex);
                        log.info("正在保存第 {} 批关联记录，数量: {} (总进度: {}/{})",
                                (i / batchSize + 1), batch.size(), endIndex, categoriesToStore.size());
                        // 使用 mapper 直接批量插入而不是通过 service
                        categoriesImagesMapper.insertBatch(batch);
                    }
                }
            } else {
                log.warn("在目录 {} 中未找到支持的图片。", resolvedPath);
            }

        } catch (IOException e) {
            log.error("处理目录失败: {}", resolvedPath, e);
            throw new RuntimeException("处理目录失败: " + resolvedPath, e);
        }
        // 清理临时目录的逻辑已移至 AnnotationImageSourcesService, 因为现在处理的是永久解压目录
    }

    private boolean isSupportedImage(Path path) {
        String fileName = path.toString().toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.stream().anyMatch(fileName::endsWith);
    }

    /**
     * 解析目录路径：如果是相对路径，则基于 annotation_data 根目录进行解析
     * @param directoryPath 输入的目录路径
     * @return 解析后的绝对路径
     */
    private Path resolveDirectoryPath(Path directoryPath) {
        // 如果路径已经是绝对路径，直接返回
        if (directoryPath.isAbsolute()) {
            return directoryPath;
        }

        // 如果是相对路径，基于 annotation_data 根目录进行解析
        Path basePath = Paths.get(pathProperties.getBasePath());
        Path resolvedPath = basePath.resolve(directoryPath);

        log.info("路径解析: {} -> {}", directoryPath, resolvedPath);
        return resolvedPath;
    }
}
